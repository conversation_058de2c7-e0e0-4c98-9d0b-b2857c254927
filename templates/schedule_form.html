<!DOCTYPE html>
<html>
<head>
    <title>Schedule Generator</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input[type="text"] { width: 90%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .class-inputs { display: grid; grid-template-columns: repeat(2, 1fr); gap: 10px; }
        .class-inputs .form-group { margin-bottom: 0; }
        #import-area { display: none;}
        #import-area textarea { width: 100%; height: 150px; }
        #import-area button { margin-top: 5px; background: #28a745; }
        .also-note { font-style: italic; font-size: 0.85em; color: #555; margin-top: 2px; }
    </style>
</head>
<body>
    <h1>Schedule Generator</h1>
    Welcome to the Schedule Generator! This tool helps you create a beautiful, customized school schedule. For information on how to use this tool, please see <a href="{{ url_for('guide') }}">the guide</a>.
    <br><br>
    <form method="POST" action="/generate" id="schedule-form">
        <div class="form-group">
            <label for="title">Schedule Title:</label>
            <input type="text" id="title" name="title" value="{{ title }}" placeholder="Enter schedule title">
        </div>

        <div class="form-group">
            <label for="free-block">Free Block Name:</label>
            <input type="text" id="free-block" name="free_block" value="{{ free_block }}" placeholder="Study Period">
        </div>

        <div class="form-group">
            <label>Classes:</label>
            <button type="button" id="import-btn" style="margin-bottom:10px;">Import Classes</button>

            <div id="import-area">
                <label for="import-text">Paste JSON here:</label>
                <textarea id="import-text" placeholder=''></textarea>
                <button type="button" id="import-apply-btn">Apply</button>
                <button type="button" id="import-cancel-btn">Cancel</button>
                <div id="import-error" style="color:red; margin-top:5px;"></div>
            </div>

            <div class="class-inputs">
                {% for i in range(1,9) %}
                <div class="form-group">
                    <label for="class-{{ i }}">Period {{ i }}:</label>
                    <input type="text" id="class-{{ i }}" name="class-{{ i }}" value="{{ classes.get(i|string, '') }}" placeholder="Class for period {{ i }}">
                    <input type="text" id="room-{{ i }}" name="room-{{ i }}" value="{{ rooms.get(i|string, '') }}" placeholder="Room for period {{ i }}" style="margin-top:5px; width:90%; padding:8px; border:1px solid #ddd; border-radius:4px;">
                    <input type="text" id="teacher-{{ i }}" name="teacher-{{ i }}" value="{{ teachers.get(i|string, '') }}" placeholder="Teacher for period {{ i }}" style="margin-top:5px; width:90%; padding:8px; border:1px solid #ddd; border-radius:4px;">
                </div>
                {% endfor %}
            </div>
        </div>

        <button type="submit">Generate Schedule</button>
    </form>

<script>
document.getElementById('import-btn').addEventListener('click', () => {
    document.getElementById('import-area').style.display = 'block';
    document.getElementById('import-error').textContent = '';
});

document.getElementById('import-cancel-btn').addEventListener('click', () => {
    document.getElementById('import-area').style.display = 'none';
    document.getElementById('import-text').value = '';
    document.getElementById('import-error').textContent = '';
});

document.getElementById('import-apply-btn').addEventListener('click', () => {
    let text = document.getElementById('import-text').value.trim();
    if (!text) {
        document.getElementById('import-error').textContent = 'Please paste JSON text.';
        return;
    }

    try {
        let data = JSON.parse(text);
        document.getElementById('import-error').textContent = '';

        for (let i = 1; i <= 8; i++) {
            let input = document.getElementById('class-' + i);
            let roomInput = document.getElementById('room-' + i);
            let teacherInput = document.getElementById('teacher-' + i);
            let formGroup = input.parentElement;

            // Clear old "also" notes
            let oldNote = formGroup.querySelector(".also-note");
            if (oldNote) oldNote.remove();

            let periodData = data[i] || data[i.toString()];
            if (periodData && Array.isArray(periodData.name)) {
                let nameList = periodData.name;

                if (nameList.length > 0) {
                    let foundationsIndex = nameList.findIndex(v => v.includes("Foundations"));
                    let mainValue = foundationsIndex !== -1 ? nameList[foundationsIndex] : nameList[0];
                    let mainValueIndex = foundationsIndex !== -1 ? foundationsIndex : 0;
                    let alsoValues = nameList.filter((_, idx) => idx !== mainValueIndex);

                    input.value = mainValue || "";

                    // Handle rooms - use the same index logic as classes
                    let mainRoomIndex = mainValueIndex;
                    if (periodData.room && Array.isArray(periodData.room) && periodData.room.length > 0) {
                        // Use the room that corresponds to the main class
                        if (mainRoomIndex < periodData.room.length) {
                            roomInput.value = periodData.room[mainRoomIndex] || "";
                        } else {
                            roomInput.value = periodData.room[0] || "";
                        }
                    } else {
                        roomInput.value = "";
                    }

                    // Handle teachers - use the same index logic as classes
                    let mainTeacherIndex = mainValueIndex;
                    if (periodData.teacher && Array.isArray(periodData.teacher) && periodData.teacher.length > 0) {
                        // Use the teacher that corresponds to the main class
                        if (mainTeacherIndex < periodData.teacher.length) {
                            teacherInput.value = periodData.teacher[mainTeacherIndex] || "";
                        } else {
                            teacherInput.value = periodData.teacher[0] || "";
                        }
                    } else {
                        teacherInput.value = "";
                    }

                    // Collect all "also" items
                    let alsoItems = [];

                    // Add class names (only if there are other classes besides the main one)
                    if (alsoValues.length > 0) {
                        alsoItems.push("classes: " + alsoValues.join(", "));
                    }

                    // Add class numbers if available
                    if (periodData.number && Array.isArray(periodData.number) && periodData.number.length > 0) {
                        alsoItems.push("numbers: " + periodData.number.join(", "));
                    }

                    // Add other rooms if there are multiple rooms
                    if (periodData.room && Array.isArray(periodData.room) && periodData.room.length > 1) {
                        let otherRooms = periodData.room.filter((room, idx) => idx !== mainRoomIndex && room.trim() !== "");
                        if (otherRooms.length > 0) {
                            alsoItems.push("rooms: " + otherRooms.join(", "));
                        }
                    }

                    // Add other teachers if there are multiple teachers
                    if (periodData.teacher && Array.isArray(periodData.teacher) && periodData.teacher.length > 1) {
                        let otherTeachers = periodData.teacher.filter((teacher, idx) => idx !== mainTeacherIndex && teacher.trim() !== "");
                        if (otherTeachers.length > 0) {
                            alsoItems.push("teachers: " + otherTeachers.join(", "));
                        }
                    }

                    // Create the also note if there are any items
                    if (alsoItems.length > 0) {
                        let note = document.createElement("div");
                        note.className = "also-note";
                        note.textContent = "also " + alsoItems.join("; ");
                        formGroup.appendChild(note);
                    }
                } else {
                    input.value = "";
                    roomInput.value = "";
                    teacherInput.value = "";
                }
            } else {
                input.value = "";
                roomInput.value = "";
                teacherInput.value = "";
            }
        }

        document.getElementById('import-area').style.display = 'none';
        document.getElementById('import-text').value = '';

    } catch (e) {
        document.getElementById('import-error').textContent = 'Invalid JSON: ' + e.message;
    }
});

document.getElementById('schedule-form').addEventListener('submit', (e) => {
    let existing = document.getElementById('classes-json');
    if (existing) existing.remove();

    let classesObj = {};
    for (let i = 1; i <= 8; i++) {
        let classVal = document.getElementById('class-' + i).value.trim();
        let roomVal = document.getElementById('room-' + i).value.trim();
        let teacherVal = document.getElementById('teacher-' + i).value.trim();

        if (classVal || roomVal || teacherVal) {
            classesObj[i] = {
                name: classVal || "",
                room: roomVal || "",
                teacher: teacherVal || ""
            };
        }
    }

    let hidden = document.createElement('input');
    hidden.type = 'hidden';
    hidden.name = 'classes';
    hidden.id = 'classes-json';
    hidden.value = JSON.stringify(classesObj);

    e.target.appendChild(hidden);
});
</script>

</body>
</html>
