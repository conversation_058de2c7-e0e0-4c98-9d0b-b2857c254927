import svg
from PIL import ImageFont
# import cairosvg

OTHER_HEIGHT = 132
CLASS_HEIGHT = 233
BREAK_HEIGHT = 55
TITLE_HEIGHT = 100
XDAY_CLASS_HEIGHT = ((OTHER_HEIGHT*3 + CLASS_HEIGHT*4 + BREAK_HEIGHT*2) - (3*BREAK_HEIGHT) - (2*OTHER_HEIGHT)) / 8

WIDTH = 423

XDAY_GAP = 65

START_X = 200
START_Y = 200

MAIN_OFFSET_X = XDAY_GAP + (WIDTH * 2)
MAIN_OFFSET_Y = TITLE_HEIGHT

TIMES_OFFSET_X = XDAY_GAP + WIDTH
TIMES_OFFSET_Y = TITLE_HEIGHT

XDAY_OFFSET_X = 0
XDAY_OFFSET_Y = TITLE_HEIGHT

STROKE_WIDTH = 4

BASE_CLASS_TEXT_SIZE = 50
BASE_OTHER_TEXT_SIZE = 40
BASE_TIMES_TEXT_SIZE = 48
BASE_TITLE_TEXT_SIZE = 150
BASE_DAY_NUMBERS_TEXT_SIZE = 100

MAIN_FONT_FAMILY = "Inter"
TITLE_FONT_FAMILY = "Jomhuria"

CLASS_COLORS = {
    1: "#91DCFA",
    2: "#FEF59C",
    3: "#FFC4E7",
    4: "#E3B5FB",
    5: "#FF9795",
    6: "#A7F0D6",
    7: "#FDC688",
    8: "#CCCCCC"
}

CLASSES = {
    1: "Global History",
    2: "Free",
    3: "English I",
    4: "Accelerated Physics",
    5: "Foundations",
    6: "Graphic Design",
    7: "Spanish II",
    8: "Math I (H)"
}

TITLE_TEXT = "Simon's 9th Grade Timetable"

FULL_WIDTH = WIDTH*6 + XDAY_GAP
CANVAS_WIDTH = START_X*2+FULL_WIDTH
CANVAS_HEIGHT = START_Y*2+(MAIN_OFFSET_Y+OTHER_HEIGHT*3+CLASS_HEIGHT*4+BREAK_HEIGHT*2)

def measure_text_width(text, font_size, font_path="/Users/<USER>/Library/Fonts/Inter-VariableFont_opsz,wght.ttf"):
    """Measure text width using Pillow for accurate font metrics."""
    font = ImageFont.truetype(font_path, font_size)
    return font.getlength(text)  # getlength() gives width in pixels

def fit_text_to_width(text, max_width, base_size=50, min_size=8, font_path="/Users/<USER>/Library/Fonts/Inter-VariableFont_opsz,wght.ttf"):
    """Finds the largest font size that fits within max_width using real metrics."""
    # Check if it fits at base size first
    if measure_text_width(text, base_size, font_path) <= max_width:
        return base_size

    font_size = base_size
    while font_size > min_size:
        font_size -= 1
        if measure_text_width(text, font_size, font_path) <= max_width:
            print("Modified:", font_size)
            return font_size

    print("MINIMUM:", min_size)
    return min_size



def draw_day(x, y, daily_classes, classes):
    elements = [
        svg.Rect(
            x=x, y=y+OTHER_HEIGHT,
            width=WIDTH, height=CLASS_HEIGHT,
            stroke="black",
            fill=CLASS_COLORS[daily_classes[0]],
            stroke_width=STROKE_WIDTH,
        ),
        svg.Rect(
            x=x, y=y+OTHER_HEIGHT+CLASS_HEIGHT+BREAK_HEIGHT,
            width=WIDTH, height=CLASS_HEIGHT,
            stroke="black",
            fill=CLASS_COLORS[daily_classes[1]],
            stroke_width=STROKE_WIDTH,
        ),
        svg.Rect(
            x=x, y=y+OTHER_HEIGHT+CLASS_HEIGHT+BREAK_HEIGHT+OTHER_HEIGHT+CLASS_HEIGHT,
            width=WIDTH, height=CLASS_HEIGHT,
            stroke="black",
            fill=CLASS_COLORS[daily_classes[2]],
            stroke_width=STROKE_WIDTH,
        ),
        svg.Rect(
            x=x, y=y+OTHER_HEIGHT+CLASS_HEIGHT+BREAK_HEIGHT+OTHER_HEIGHT+CLASS_HEIGHT+BREAK_HEIGHT+CLASS_HEIGHT,
            width=WIDTH, height=CLASS_HEIGHT,
            stroke="black",
            fill=CLASS_COLORS[daily_classes[3]],
            stroke_width=STROKE_WIDTH,
        ),
        svg.Rect(
            x=x, y=y+OTHER_HEIGHT+CLASS_HEIGHT+BREAK_HEIGHT+OTHER_HEIGHT+CLASS_HEIGHT+BREAK_HEIGHT+CLASS_HEIGHT+CLASS_HEIGHT,
            width=WIDTH, height=OTHER_HEIGHT,
            stroke="black",
            fill="#FFFFFF",
            stroke_width=STROKE_WIDTH,
        ),
    ]

    text_content = str(classes[daily_classes[0]])
    font_size = fit_text_to_width(text_content, WIDTH - 20, base_size=BASE_CLASS_TEXT_SIZE)
    elements.append(svg.Text(
        text=text_content,
        x=x + WIDTH / 2,
        y=y + OTHER_HEIGHT+CLASS_HEIGHT/2,
        text_anchor="middle",
        dominant_baseline="central",
        font_size=font_size,
        font_family=MAIN_FONT_FAMILY
    ))

    text_content = str(classes[daily_classes[1]])
    font_size = fit_text_to_width(text_content, WIDTH - 20, base_size=BASE_CLASS_TEXT_SIZE)
    elements.append(svg.Text(
        text=text_content,
        x=x + WIDTH / 2,
        y=y + OTHER_HEIGHT+(CLASS_HEIGHT/2)+CLASS_HEIGHT+BREAK_HEIGHT,
        text_anchor="middle",
        dominant_baseline="central",
        font_size=font_size,
        font_family=MAIN_FONT_FAMILY
    ))

    text_content = str(classes[daily_classes[2]])
    font_size = fit_text_to_width(text_content, WIDTH - 20, base_size=BASE_CLASS_TEXT_SIZE)
    elements.append(svg.Text(
        text=text_content,
        x=x + WIDTH / 2,
        y=y + OTHER_HEIGHT+(CLASS_HEIGHT/2)+CLASS_HEIGHT+BREAK_HEIGHT+OTHER_HEIGHT+CLASS_HEIGHT,
        text_anchor="middle",
        dominant_baseline="central",
        font_size=font_size,
        font_family=MAIN_FONT_FAMILY
    ))

    text_content = str(classes[daily_classes[3]])
    font_size = fit_text_to_width(text_content, WIDTH - 20, base_size=BASE_CLASS_TEXT_SIZE)
    elements.append(svg.Text(
        text=text_content,
        x=x + WIDTH / 2,
        y=y + OTHER_HEIGHT+(CLASS_HEIGHT/2)+CLASS_HEIGHT+BREAK_HEIGHT+OTHER_HEIGHT+CLASS_HEIGHT+BREAK_HEIGHT+CLASS_HEIGHT,
        text_anchor="middle",
        dominant_baseline="central",
        font_size=font_size,
        font_family=MAIN_FONT_FAMILY
    ))

    return elements


def draw_sames(x, y):
    elements = [
        svg.Rect(
            x=x, y=y,
            width=WIDTH*2, height=OTHER_HEIGHT,
            stroke="black",
            fill="#AB91FA",
            stroke_width=STROKE_WIDTH,
        ),
        svg.Rect(
            x=x+WIDTH*2, y=y,
            width=WIDTH, height=OTHER_HEIGHT,
            stroke="black",
            fill="#AB91FA",
            stroke_width=STROKE_WIDTH,
        ),
        svg.Rect(
            x=x+WIDTH*3, y=y,
            width=WIDTH, height=OTHER_HEIGHT,
            stroke="black",
            fill="#AB91FA",
            stroke_width=STROKE_WIDTH,
        ),
        svg.Rect(
            x=x, y=y+OTHER_HEIGHT+CLASS_HEIGHT,
            width=WIDTH*4, height=BREAK_HEIGHT,
            stroke="black",
            fill="#00B6FF",
            stroke_width=STROKE_WIDTH,
        ),
        svg.Rect(
            x=x, y=y+OTHER_HEIGHT+CLASS_HEIGHT+BREAK_HEIGHT+CLASS_HEIGHT,
            width=WIDTH*4, height=OTHER_HEIGHT,
            stroke="black",
            fill="#A8FA91",
            stroke_width=STROKE_WIDTH,
        ),
        svg.Rect(
            x=x, y=y+OTHER_HEIGHT+CLASS_HEIGHT+BREAK_HEIGHT+CLASS_HEIGHT+OTHER_HEIGHT+CLASS_HEIGHT,
            width=WIDTH*4, height=BREAK_HEIGHT,
            stroke="black",
            fill="#00B6FF",
            stroke_width=STROKE_WIDTH,
        ),
        svg.Text(
            text='Office Hours',
            x=x + WIDTH,
            y=y + (OTHER_HEIGHT/2),
            text_anchor="middle",
            dominant_baseline="central",
            font_size=BASE_OTHER_TEXT_SIZE,
            font_family=MAIN_FONT_FAMILY
        ),
        svg.Text(
            text='Faculty Meeting',
            x=x + WIDTH*2.5,
            y=y + (OTHER_HEIGHT/2),
            text_anchor="middle",
            dominant_baseline="central",
            font_size=BASE_OTHER_TEXT_SIZE,
            font_family=MAIN_FONT_FAMILY
        ),
        svg.Text(
            text='Office Hours',
            x=x + WIDTH*3.5,
            y=y + (OTHER_HEIGHT/2),
            text_anchor="middle",
            dominant_baseline="central",
            font_size=BASE_OTHER_TEXT_SIZE,
            font_family=MAIN_FONT_FAMILY
        ),
        svg.Text(
            text='Break',
            x=x + WIDTH*2,
            y=y + OTHER_HEIGHT+CLASS_HEIGHT+(BREAK_HEIGHT/2),
            text_anchor="middle",
            dominant_baseline="central",
            font_size=BASE_OTHER_TEXT_SIZE,
            font_family=MAIN_FONT_FAMILY
        ),
        svg.Text(
            text='Lunch',
            x=x + WIDTH*2,
            y=y + OTHER_HEIGHT+CLASS_HEIGHT+BREAK_HEIGHT+CLASS_HEIGHT+(OTHER_HEIGHT/2),
            text_anchor="middle",
            dominant_baseline="central",
            font_size=BASE_CLASS_TEXT_SIZE,
            font_family=MAIN_FONT_FAMILY
        ),
        svg.Text(
            text='Break',
            x=x + WIDTH*2,
            y=y + OTHER_HEIGHT+CLASS_HEIGHT+BREAK_HEIGHT+CLASS_HEIGHT+OTHER_HEIGHT+CLASS_HEIGHT+(BREAK_HEIGHT/2),
            text_anchor="middle",
            dominant_baseline="central",
            font_size=BASE_OTHER_TEXT_SIZE,
            font_family=MAIN_FONT_FAMILY
        ),
        svg.Text(
            text='Flex 1 or Flex 3',
            x=x+WIDTH*0.5,
            y=y + OTHER_HEIGHT+CLASS_HEIGHT+BREAK_HEIGHT+CLASS_HEIGHT+OTHER_HEIGHT+CLASS_HEIGHT+BREAK_HEIGHT+CLASS_HEIGHT+(OTHER_HEIGHT*0.35),
            text_anchor="middle",
            dominant_baseline="central",
            font_size=BASE_OTHER_TEXT_SIZE,
            font_family=MAIN_FONT_FAMILY
        ),
        svg.Text(
            text='Advisory',
            x=x+WIDTH*1.5,
            y=y + OTHER_HEIGHT+CLASS_HEIGHT+BREAK_HEIGHT+CLASS_HEIGHT+OTHER_HEIGHT+CLASS_HEIGHT+BREAK_HEIGHT+CLASS_HEIGHT+(OTHER_HEIGHT*0.35),
            text_anchor="middle",
            dominant_baseline="central",
            font_size=BASE_OTHER_TEXT_SIZE,
            font_family=MAIN_FONT_FAMILY
        ),
        svg.Text(
            text='Flex 2 or Flex 4',
            x=x+WIDTH*2.5,
            y=y + OTHER_HEIGHT+CLASS_HEIGHT+BREAK_HEIGHT+CLASS_HEIGHT+OTHER_HEIGHT+CLASS_HEIGHT+BREAK_HEIGHT+CLASS_HEIGHT+(OTHER_HEIGHT*0.35),
            text_anchor="middle",
            dominant_baseline="central",
            font_size=BASE_OTHER_TEXT_SIZE,
            font_family=MAIN_FONT_FAMILY
        ),
        svg.Text(
            text='Assembly',
            x=x+WIDTH*3.5,
            y=y + OTHER_HEIGHT+CLASS_HEIGHT+BREAK_HEIGHT+CLASS_HEIGHT+OTHER_HEIGHT+CLASS_HEIGHT+BREAK_HEIGHT+CLASS_HEIGHT+(OTHER_HEIGHT*0.35),
            text_anchor="middle",
            dominant_baseline="central",
            font_size=BASE_OTHER_TEXT_SIZE,
            font_family=MAIN_FONT_FAMILY
        ),
        svg.Text(
            text='and or office hours',
            x=x+WIDTH*0.5,
            y=y + OTHER_HEIGHT+CLASS_HEIGHT+BREAK_HEIGHT+CLASS_HEIGHT+OTHER_HEIGHT+CLASS_HEIGHT+BREAK_HEIGHT+CLASS_HEIGHT+(OTHER_HEIGHT*0.65),
            text_anchor="middle",
            dominant_baseline="central",
            font_size=BASE_OTHER_TEXT_SIZE,
            font_family=MAIN_FONT_FAMILY
        ),
        svg.Text(
            text='and or office hours',
            x=x+WIDTH*1.5,
            y=y + OTHER_HEIGHT+CLASS_HEIGHT+BREAK_HEIGHT+CLASS_HEIGHT+OTHER_HEIGHT+CLASS_HEIGHT+BREAK_HEIGHT+CLASS_HEIGHT+(OTHER_HEIGHT*0.65),
            text_anchor="middle",
            dominant_baseline="central",
            font_size=BASE_OTHER_TEXT_SIZE,
            font_family=MAIN_FONT_FAMILY
        ),
        svg.Text(
            text='and or office hours',
            x=x+WIDTH*2.5,
            y=y + OTHER_HEIGHT+CLASS_HEIGHT+BREAK_HEIGHT+CLASS_HEIGHT+OTHER_HEIGHT+CLASS_HEIGHT+BREAK_HEIGHT+CLASS_HEIGHT+(OTHER_HEIGHT*0.65),
            text_anchor="middle",
            dominant_baseline="central",
            font_size=BASE_OTHER_TEXT_SIZE,
            font_family=MAIN_FONT_FAMILY
        ),
        svg.Text(
            text='and or office hours',
            x=x+WIDTH*3.5,
            y=y + OTHER_HEIGHT+CLASS_HEIGHT+BREAK_HEIGHT+CLASS_HEIGHT+OTHER_HEIGHT+CLASS_HEIGHT+BREAK_HEIGHT+CLASS_HEIGHT+(OTHER_HEIGHT*0.65),
            text_anchor="middle",
            dominant_baseline="central",
            font_size=BASE_OTHER_TEXT_SIZE,
            font_family=MAIN_FONT_FAMILY
        )
    ]

    return elements

def draw_times(x, y):
    elements = [
        svg.Rect(
            x=x, y=y,
            width=WIDTH, height=OTHER_HEIGHT,
            stroke="black",
            fill="#777777",
            stroke_width=STROKE_WIDTH,
        ),
        svg.Rect(
            x=x, y=y+OTHER_HEIGHT,
            width=WIDTH, height=CLASS_HEIGHT,
            stroke="black",
            fill="#777777",
            stroke_width=STROKE_WIDTH,
        ),
        svg.Rect(
            x=x, y=y+OTHER_HEIGHT+CLASS_HEIGHT,
            width=WIDTH, height=BREAK_HEIGHT,
            stroke="black",
            fill="#777777",
            stroke_width=STROKE_WIDTH,
        ),
        svg.Rect(
            x=x, y=y+OTHER_HEIGHT+CLASS_HEIGHT+BREAK_HEIGHT,
            width=WIDTH, height=CLASS_HEIGHT,
            stroke="black",
            fill="#777777",
            stroke_width=STROKE_WIDTH,
        ),
        svg.Rect(
            x=x, y=y+OTHER_HEIGHT+CLASS_HEIGHT+BREAK_HEIGHT+CLASS_HEIGHT,
            width=WIDTH, height=OTHER_HEIGHT,
            stroke="black",
            fill="#777777",
            stroke_width=STROKE_WIDTH,
        ),
        svg.Rect(
            x=x, y=y+OTHER_HEIGHT+CLASS_HEIGHT+BREAK_HEIGHT+CLASS_HEIGHT+OTHER_HEIGHT,
            width=WIDTH, height=CLASS_HEIGHT,
            stroke="black",
            fill="#777777",
            stroke_width=STROKE_WIDTH,
        ),
        svg.Rect(
            x=x, y=y+OTHER_HEIGHT+CLASS_HEIGHT+BREAK_HEIGHT+CLASS_HEIGHT+OTHER_HEIGHT+CLASS_HEIGHT,
            width=WIDTH, height=BREAK_HEIGHT,
            stroke="black",
            fill="#777777",
            stroke_width=STROKE_WIDTH,
        ),
        svg.Rect(
            x=x, y=y+OTHER_HEIGHT+CLASS_HEIGHT+BREAK_HEIGHT+CLASS_HEIGHT+OTHER_HEIGHT+CLASS_HEIGHT+BREAK_HEIGHT,
            width=WIDTH, height=CLASS_HEIGHT,
            stroke="black",
            fill="#777777",
            stroke_width=STROKE_WIDTH,
        ),
        svg.Rect(
            x=x, y=y+OTHER_HEIGHT+CLASS_HEIGHT+BREAK_HEIGHT+CLASS_HEIGHT+OTHER_HEIGHT+CLASS_HEIGHT+BREAK_HEIGHT+CLASS_HEIGHT,
            width=WIDTH, height=OTHER_HEIGHT,
            stroke="black",
            fill="#777777",
            stroke_width=STROKE_WIDTH,
        ),
        # -- TEXT --

        svg.Text(
            text="7:50-8:20 a.m.",
            x=x+WIDTH/2, y=y+(OTHER_HEIGHT/2),
            text_anchor="middle",
            dominant_baseline="central",
            font_size=BASE_TIMES_TEXT_SIZE,
            font_family=MAIN_FONT_FAMILY
        ),
        svg.Text(
            text="8:30 - 9:45 a.m.",
            x=x+WIDTH/2, y=y+OTHER_HEIGHT+(CLASS_HEIGHT/2),
            text_anchor="middle",
            dominant_baseline="central",
            font_size=BASE_TIMES_TEXT_SIZE,
            font_family=MAIN_FONT_FAMILY
        ),
        svg.Text(
            text="9:45 - 9:55 a.m.",
            x=x+WIDTH/2, y=y+OTHER_HEIGHT+CLASS_HEIGHT+(BREAK_HEIGHT/2),
            text_anchor="middle",
            dominant_baseline="central",
            font_size=BASE_TIMES_TEXT_SIZE,
            font_family=MAIN_FONT_FAMILY
        ),
        svg.Text(
            text="9:55 - 11:10 a.m.",
            x=x+WIDTH/2, y=y+OTHER_HEIGHT+CLASS_HEIGHT+BREAK_HEIGHT+(CLASS_HEIGHT/2),
            text_anchor="middle",
            dominant_baseline="central",
            font_size=BASE_TIMES_TEXT_SIZE,
            font_family=MAIN_FONT_FAMILY
        ),
        svg.Text(
            text="11:10 - 11:50 a.m.",
            x=x+WIDTH/2, y=y+OTHER_HEIGHT+CLASS_HEIGHT+BREAK_HEIGHT+CLASS_HEIGHT+(OTHER_HEIGHT/2),
            text_anchor="middle",
            dominant_baseline="central",
            font_size=BASE_TIMES_TEXT_SIZE,
            font_family=MAIN_FONT_FAMILY
        ),
        svg.Text(
            text="11:50am-1:05 pm",
            x=x+WIDTH/2, y=y+OTHER_HEIGHT+CLASS_HEIGHT+BREAK_HEIGHT+CLASS_HEIGHT+OTHER_HEIGHT+(CLASS_HEIGHT/2),
            text_anchor="middle",
            dominant_baseline="central",
            font_size=BASE_TIMES_TEXT_SIZE,
            font_family=MAIN_FONT_FAMILY
        ),
        svg.Text(
            text="1:05 - 1:15 p.m.",
            x=x+WIDTH/2, y=y+OTHER_HEIGHT+CLASS_HEIGHT+BREAK_HEIGHT+CLASS_HEIGHT+OTHER_HEIGHT+CLASS_HEIGHT+(BREAK_HEIGHT/2),
            text_anchor="middle",
            dominant_baseline="central",
            font_size=BASE_TIMES_TEXT_SIZE,
            font_family=MAIN_FONT_FAMILY
        ),
        svg.Text(
            text="1:15 - 2:30 p.m.",
            x=x+WIDTH/2, y=y+OTHER_HEIGHT+CLASS_HEIGHT+BREAK_HEIGHT+CLASS_HEIGHT+OTHER_HEIGHT+CLASS_HEIGHT+BREAK_HEIGHT+(CLASS_HEIGHT/2),
            text_anchor="middle",
            dominant_baseline="central",
            font_size=BASE_TIMES_TEXT_SIZE,
            font_family=MAIN_FONT_FAMILY
        ),
        svg.Text(
            text="2:30 - 3:15 p.m.",
            x=x+WIDTH/2, y=y+OTHER_HEIGHT+CLASS_HEIGHT+BREAK_HEIGHT+CLASS_HEIGHT+OTHER_HEIGHT+CLASS_HEIGHT+BREAK_HEIGHT+CLASS_HEIGHT+(OTHER_HEIGHT/2),
            text_anchor="middle",
            dominant_baseline="central",
            font_size=BASE_TIMES_TEXT_SIZE,
            font_family=MAIN_FONT_FAMILY
        ),
    ]

    return elements


def draw_xday(x, y, classes):
    elements = [
        svg.Rect(
            x=x, y=y,
            width=WIDTH, height=OTHER_HEIGHT,
            stroke="black",
            fill="#AB91FA",
            stroke_width=STROKE_WIDTH,
        ),
        svg.Rect(
            x=x, y=y+OTHER_HEIGHT,
            width=WIDTH, height=XDAY_CLASS_HEIGHT,
            stroke="black",
            fill=CLASS_COLORS[1],
            stroke_width=STROKE_WIDTH,
        ),
        svg.Rect(
            x=x, y=y+OTHER_HEIGHT+XDAY_CLASS_HEIGHT,
            width=WIDTH, height=XDAY_CLASS_HEIGHT,
            stroke="black",
            fill=CLASS_COLORS[2],
            stroke_width=STROKE_WIDTH,
        ),
        svg.Rect(
            x=x, y=y+OTHER_HEIGHT+XDAY_CLASS_HEIGHT+XDAY_CLASS_HEIGHT,
            width=WIDTH, height=BREAK_HEIGHT,
            stroke="black",
            fill="#00B6FF",
            stroke_width=STROKE_WIDTH,
        ),
        svg.Rect(
            x=x, y=y+OTHER_HEIGHT+XDAY_CLASS_HEIGHT+XDAY_CLASS_HEIGHT+BREAK_HEIGHT,
            width=WIDTH, height=XDAY_CLASS_HEIGHT,
            stroke="black",
            fill=CLASS_COLORS[3],
            stroke_width=STROKE_WIDTH,
        ),
        svg.Rect(
            x=x, y=y+OTHER_HEIGHT+XDAY_CLASS_HEIGHT+XDAY_CLASS_HEIGHT+BREAK_HEIGHT+XDAY_CLASS_HEIGHT,
            width=WIDTH, height=XDAY_CLASS_HEIGHT,
            stroke="black",
            fill=CLASS_COLORS[4],
            stroke_width=STROKE_WIDTH,
        ),
        svg.Rect(
            x=x, y=y+OTHER_HEIGHT+XDAY_CLASS_HEIGHT+XDAY_CLASS_HEIGHT+BREAK_HEIGHT+XDAY_CLASS_HEIGHT+XDAY_CLASS_HEIGHT,
            width=WIDTH, height=BREAK_HEIGHT,
            stroke="black",
            fill="#00B6FF",
            stroke_width=STROKE_WIDTH,
        ),
        svg.Rect(
            x=x, y=y+OTHER_HEIGHT+XDAY_CLASS_HEIGHT+XDAY_CLASS_HEIGHT+BREAK_HEIGHT+XDAY_CLASS_HEIGHT+XDAY_CLASS_HEIGHT+BREAK_HEIGHT,
            width=WIDTH, height=XDAY_CLASS_HEIGHT,
            stroke="black",
            fill=CLASS_COLORS[5],
            stroke_width=STROKE_WIDTH,
        ),
        svg.Rect(
            x=x, y=y+OTHER_HEIGHT+XDAY_CLASS_HEIGHT+XDAY_CLASS_HEIGHT+BREAK_HEIGHT+XDAY_CLASS_HEIGHT+XDAY_CLASS_HEIGHT+BREAK_HEIGHT+XDAY_CLASS_HEIGHT,
            width=WIDTH, height=OTHER_HEIGHT,
            stroke="black",
            fill="#A8FA91",
            stroke_width=STROKE_WIDTH,
        ),
        svg.Rect(
            x=x, y=y+OTHER_HEIGHT+XDAY_CLASS_HEIGHT+XDAY_CLASS_HEIGHT+BREAK_HEIGHT+XDAY_CLASS_HEIGHT+XDAY_CLASS_HEIGHT+BREAK_HEIGHT+XDAY_CLASS_HEIGHT+OTHER_HEIGHT,
            width=WIDTH, height=XDAY_CLASS_HEIGHT,
            stroke="black",
            fill=CLASS_COLORS[6],
            stroke_width=STROKE_WIDTH,
        ),
        svg.Rect(
            x=x, y=y+OTHER_HEIGHT+XDAY_CLASS_HEIGHT+XDAY_CLASS_HEIGHT+BREAK_HEIGHT+XDAY_CLASS_HEIGHT+XDAY_CLASS_HEIGHT+BREAK_HEIGHT+XDAY_CLASS_HEIGHT+OTHER_HEIGHT+XDAY_CLASS_HEIGHT,
            width=WIDTH, height=XDAY_CLASS_HEIGHT,
            stroke="black",
            fill=CLASS_COLORS[7],
            stroke_width=STROKE_WIDTH,
        ),
        svg.Rect(
            x=x, y=y+OTHER_HEIGHT+XDAY_CLASS_HEIGHT+XDAY_CLASS_HEIGHT+BREAK_HEIGHT+XDAY_CLASS_HEIGHT+XDAY_CLASS_HEIGHT+BREAK_HEIGHT+XDAY_CLASS_HEIGHT+OTHER_HEIGHT+XDAY_CLASS_HEIGHT+XDAY_CLASS_HEIGHT,
            width=WIDTH, height=BREAK_HEIGHT,
            stroke="black",
            fill="#00B6FF",
            stroke_width=STROKE_WIDTH,
        ),
        svg.Rect(
            x=x, y=y+OTHER_HEIGHT+XDAY_CLASS_HEIGHT+XDAY_CLASS_HEIGHT+BREAK_HEIGHT+XDAY_CLASS_HEIGHT+XDAY_CLASS_HEIGHT+BREAK_HEIGHT+XDAY_CLASS_HEIGHT+OTHER_HEIGHT+XDAY_CLASS_HEIGHT+XDAY_CLASS_HEIGHT+BREAK_HEIGHT,
            width=WIDTH, height=XDAY_CLASS_HEIGHT,
            stroke="black",
            fill=CLASS_COLORS[8],
            stroke_width=STROKE_WIDTH,
        ),

        # -- TEXT --

        svg.Text(
            text="Office Hours",
            x=x+WIDTH/2, y=y+(OTHER_HEIGHT/2),
            text_anchor="middle",
            dominant_baseline="central",
            font_size=BASE_OTHER_TEXT_SIZE,
            font_family=MAIN_FONT_FAMILY
        ),
        svg.Text(
            text=classes[1],
            x=x + WIDTH / 2,
            y=y+OTHER_HEIGHT+(XDAY_CLASS_HEIGHT*0.3),
            text_anchor="middle",
            dominant_baseline="central",
            font_size=fit_text_to_width(classes[1], WIDTH-20, base_size=BASE_CLASS_TEXT_SIZE),
            font_family=MAIN_FONT_FAMILY
        ),
        svg.Text(
            text="8:30 - 9:10 a.m.",
            x=x+WIDTH/2, y=y+OTHER_HEIGHT+(XDAY_CLASS_HEIGHT*0.7),
            text_anchor="middle",
            dominant_baseline="central",
            font_size=BASE_OTHER_TEXT_SIZE,
            font_family=MAIN_FONT_FAMILY
        ),
        svg.Text(
            text=classes[2],
            x=x+WIDTH/2, y=y+OTHER_HEIGHT+XDAY_CLASS_HEIGHT+(XDAY_CLASS_HEIGHT*0.3),
            text_anchor="middle",
            dominant_baseline="central",
            font_size=fit_text_to_width(classes[2], WIDTH-20, base_size=BASE_CLASS_TEXT_SIZE),
            font_family=MAIN_FONT_FAMILY
        ),
        svg.Text(
            text="9:15 - 9:55 a.m.",
            x=x+WIDTH/2, y=y+OTHER_HEIGHT+XDAY_CLASS_HEIGHT+(XDAY_CLASS_HEIGHT*0.7),
            text_anchor="middle",
            dominant_baseline="central",
            font_size=BASE_OTHER_TEXT_SIZE,
            font_family=MAIN_FONT_FAMILY
        ),
        svg.Text(
            text="Break 9:55 - 10:05 a.m.",
            x=x+WIDTH/2, y=y+OTHER_HEIGHT+XDAY_CLASS_HEIGHT+XDAY_CLASS_HEIGHT+(BREAK_HEIGHT/2),
            text_anchor="middle",
            dominant_baseline="central",
            font_size=BASE_OTHER_TEXT_SIZE,
            font_family=MAIN_FONT_FAMILY
        ),
        svg.Text(
            text=classes[3],
            x=x+WIDTH/2, y=y+OTHER_HEIGHT+XDAY_CLASS_HEIGHT+XDAY_CLASS_HEIGHT+BREAK_HEIGHT+(XDAY_CLASS_HEIGHT*0.3),
            text_anchor="middle",
            dominant_baseline="central",
            font_size=fit_text_to_width(classes[3], WIDTH-20, base_size=BASE_CLASS_TEXT_SIZE),
            font_family=MAIN_FONT_FAMILY
        ),
        svg.Text(
            text="10:05 - 10:45 a.m.",
            x=x+WIDTH/2, y=y+OTHER_HEIGHT+XDAY_CLASS_HEIGHT+XDAY_CLASS_HEIGHT+BREAK_HEIGHT+(XDAY_CLASS_HEIGHT*0.7),
            text_anchor="middle",
            dominant_baseline="central",
            font_size=BASE_OTHER_TEXT_SIZE,
            font_family=MAIN_FONT_FAMILY
        ),
        svg.Text(
            text=classes[4],
            x=x+WIDTH/2, y=y+OTHER_HEIGHT+XDAY_CLASS_HEIGHT+XDAY_CLASS_HEIGHT+BREAK_HEIGHT+XDAY_CLASS_HEIGHT+(XDAY_CLASS_HEIGHT*0.3),
            text_anchor="middle",
            dominant_baseline="central",
            font_size=fit_text_to_width(classes[4], WIDTH-20, base_size=BASE_CLASS_TEXT_SIZE),
            font_family=MAIN_FONT_FAMILY
        ),
        svg.Text(
            text="10:50 - 11:30 a.m.",
            x=x+WIDTH/2, y=y+OTHER_HEIGHT+XDAY_CLASS_HEIGHT+XDAY_CLASS_HEIGHT+BREAK_HEIGHT+XDAY_CLASS_HEIGHT+(XDAY_CLASS_HEIGHT*0.7),
            text_anchor="middle",
            dominant_baseline="central",
            font_size=BASE_OTHER_TEXT_SIZE,
            font_family=MAIN_FONT_FAMILY
        ),
        svg.Text(
            text="Break 11:30 - 11:40 a.m.",
            x=x+WIDTH/2, y=y+OTHER_HEIGHT+XDAY_CLASS_HEIGHT+XDAY_CLASS_HEIGHT+BREAK_HEIGHT+XDAY_CLASS_HEIGHT+XDAY_CLASS_HEIGHT+(BREAK_HEIGHT/2),
            text_anchor="middle",
            dominant_baseline="central",
            font_size=BASE_OTHER_TEXT_SIZE,
            font_family=MAIN_FONT_FAMILY
        ),
        svg.Text(
            text=classes[5],
            x=x+WIDTH/2, y=y+OTHER_HEIGHT+XDAY_CLASS_HEIGHT+XDAY_CLASS_HEIGHT+BREAK_HEIGHT+XDAY_CLASS_HEIGHT+XDAY_CLASS_HEIGHT+BREAK_HEIGHT+(XDAY_CLASS_HEIGHT*0.3),
            text_anchor="middle",
            dominant_baseline="central",
            font_size=fit_text_to_width(classes[5], WIDTH-20, base_size=BASE_CLASS_TEXT_SIZE),
            font_family=MAIN_FONT_FAMILY
        ),
        svg.Text(
            text="11:40am - 12:20pm",
            x=x+WIDTH/2, y=y+OTHER_HEIGHT+XDAY_CLASS_HEIGHT+XDAY_CLASS_HEIGHT+BREAK_HEIGHT+XDAY_CLASS_HEIGHT+XDAY_CLASS_HEIGHT+BREAK_HEIGHT+(XDAY_CLASS_HEIGHT*0.7),
            text_anchor="middle",
            dominant_baseline="central",
            font_size=BASE_OTHER_TEXT_SIZE,
            font_family=MAIN_FONT_FAMILY
        ),
        svg.Text(
            text="Lunch",
            x=x+WIDTH/2, y=y+OTHER_HEIGHT+XDAY_CLASS_HEIGHT+XDAY_CLASS_HEIGHT+BREAK_HEIGHT+XDAY_CLASS_HEIGHT+XDAY_CLASS_HEIGHT+BREAK_HEIGHT+XDAY_CLASS_HEIGHT+(OTHER_HEIGHT*0.3),
            text_anchor="middle",
            dominant_baseline="central",
            font_size=BASE_CLASS_TEXT_SIZE,
            font_family=MAIN_FONT_FAMILY
        ),
        svg.Text(
            text="12:20 - 1:00 p.m.",
            x=x+WIDTH/2, y=y+OTHER_HEIGHT+XDAY_CLASS_HEIGHT+XDAY_CLASS_HEIGHT+BREAK_HEIGHT+XDAY_CLASS_HEIGHT+XDAY_CLASS_HEIGHT+BREAK_HEIGHT+XDAY_CLASS_HEIGHT+(OTHER_HEIGHT*0.7),
            text_anchor="middle",
            dominant_baseline="central",
            font_size=BASE_OTHER_TEXT_SIZE,
            font_family=MAIN_FONT_FAMILY
        ),
        svg.Text(
            text=classes[6],
            x=x+WIDTH/2, y=y+OTHER_HEIGHT+XDAY_CLASS_HEIGHT+XDAY_CLASS_HEIGHT+BREAK_HEIGHT+XDAY_CLASS_HEIGHT+XDAY_CLASS_HEIGHT+BREAK_HEIGHT+OTHER_HEIGHT+XDAY_CLASS_HEIGHT+(XDAY_CLASS_HEIGHT*0.3),
            text_anchor="middle",
            dominant_baseline="central",
            font_size=fit_text_to_width(classes[6], WIDTH-20, base_size=BASE_CLASS_TEXT_SIZE),
            font_family=MAIN_FONT_FAMILY
        ),
        svg.Text(
            text="1:00 - 1:40 p.m.",
            x=x+WIDTH/2, y=y+OTHER_HEIGHT+XDAY_CLASS_HEIGHT+XDAY_CLASS_HEIGHT+BREAK_HEIGHT+XDAY_CLASS_HEIGHT+XDAY_CLASS_HEIGHT+BREAK_HEIGHT+OTHER_HEIGHT+XDAY_CLASS_HEIGHT+(XDAY_CLASS_HEIGHT*0.7),
            text_anchor="middle",
            dominant_baseline="central",
            font_size=BASE_OTHER_TEXT_SIZE,
            font_family=MAIN_FONT_FAMILY
        ),
        svg.Text(
            text=classes[7],
            x=x+WIDTH/2, y=y+OTHER_HEIGHT+XDAY_CLASS_HEIGHT+XDAY_CLASS_HEIGHT+BREAK_HEIGHT+XDAY_CLASS_HEIGHT+XDAY_CLASS_HEIGHT+BREAK_HEIGHT+OTHER_HEIGHT+XDAY_CLASS_HEIGHT+XDAY_CLASS_HEIGHT+(XDAY_CLASS_HEIGHT*0.3),
            text_anchor="middle",
            dominant_baseline="central",
            font_size=fit_text_to_width(classes[7], WIDTH-20, base_size=BASE_CLASS_TEXT_SIZE),
            font_family=MAIN_FONT_FAMILY
        ),
        svg.Text(
            text="1:45 - 2:25 p.m.",
            x=x+WIDTH/2, y=y+OTHER_HEIGHT+XDAY_CLASS_HEIGHT+XDAY_CLASS_HEIGHT+BREAK_HEIGHT+XDAY_CLASS_HEIGHT+XDAY_CLASS_HEIGHT+BREAK_HEIGHT+OTHER_HEIGHT+XDAY_CLASS_HEIGHT+XDAY_CLASS_HEIGHT+(XDAY_CLASS_HEIGHT*0.7),
            text_anchor="middle",
            dominant_baseline="central",
            font_size=BASE_OTHER_TEXT_SIZE,
            font_family=MAIN_FONT_FAMILY
        ),
        svg.Text(
            text="Break 2:25 - 2:35 p.m.",
            x=x+WIDTH/2, y=y+OTHER_HEIGHT+XDAY_CLASS_HEIGHT+XDAY_CLASS_HEIGHT+BREAK_HEIGHT+XDAY_CLASS_HEIGHT+XDAY_CLASS_HEIGHT+BREAK_HEIGHT+XDAY_CLASS_HEIGHT+OTHER_HEIGHT+XDAY_CLASS_HEIGHT+XDAY_CLASS_HEIGHT+(BREAK_HEIGHT/2),
            text_anchor="middle",
            dominant_baseline="central",
            font_size=BASE_OTHER_TEXT_SIZE,
            font_family=MAIN_FONT_FAMILY
        ),
        svg.Text(
            text=classes[8],
            x=x+WIDTH/2, y=y+OTHER_HEIGHT+XDAY_CLASS_HEIGHT+XDAY_CLASS_HEIGHT+BREAK_HEIGHT+XDAY_CLASS_HEIGHT+XDAY_CLASS_HEIGHT+BREAK_HEIGHT+OTHER_HEIGHT+XDAY_CLASS_HEIGHT+XDAY_CLASS_HEIGHT+BREAK_HEIGHT+XDAY_CLASS_HEIGHT+(XDAY_CLASS_HEIGHT*0.3),
            text_anchor="middle",
            dominant_baseline="central",
            font_size=fit_text_to_width(classes[8], WIDTH-20, base_size=BASE_CLASS_TEXT_SIZE),
            font_family=MAIN_FONT_FAMILY
        ),
        svg.Text(
            text="2:35 - 3:15 p.m.",
            x=x+WIDTH/2, y=y+OTHER_HEIGHT+XDAY_CLASS_HEIGHT+XDAY_CLASS_HEIGHT+BREAK_HEIGHT+XDAY_CLASS_HEIGHT+XDAY_CLASS_HEIGHT+BREAK_HEIGHT+OTHER_HEIGHT+XDAY_CLASS_HEIGHT+XDAY_CLASS_HEIGHT+BREAK_HEIGHT+XDAY_CLASS_HEIGHT+(XDAY_CLASS_HEIGHT*0.7),
            text_anchor="middle",
            dominant_baseline="central",
            font_size=BASE_OTHER_TEXT_SIZE,
            font_family=MAIN_FONT_FAMILY
        )
    ]

    return elements

def draw_titles():
    elements = [
        svg.Rect(
            fill="#FFFFFF",
            x=0, y=0,
            width=CANVAS_WIDTH, height=CANVAS_HEIGHT
        ),
        svg.Text(
            text=TITLE_TEXT,
            x=CANVAS_WIDTH/2, y=((START_Y + MAIN_OFFSET_Y) * 0.85) / 2,
            text_anchor="middle",
            dominant_baseline="central",
            font_size=BASE_TITLE_TEXT_SIZE,
            font_family=TITLE_FONT_FAMILY
        ),
        svg.Text(
            text="X Day",
            x=START_X+(WIDTH*0.5), y=(START_Y+MAIN_OFFSET_Y)*0.85,
            text_anchor="middle",
            dominant_baseline="central",
            font_size=BASE_DAY_NUMBERS_TEXT_SIZE,
            font_family=TITLE_FONT_FAMILY
        ),
        svg.Text(
            text="Day 1",
            x=START_X+MAIN_OFFSET_X+WIDTH*0.5, y=(START_Y+MAIN_OFFSET_Y)*0.85,
            text_anchor="middle",
            dominant_baseline="central",
            font_size=BASE_DAY_NUMBERS_TEXT_SIZE,
            font_family=TITLE_FONT_FAMILY
        ),
        svg.Text(
            text="Day 2",
            x=START_X+MAIN_OFFSET_X+(WIDTH*1.5), y=(START_Y+MAIN_OFFSET_Y)*0.85,
            text_anchor="middle",
            dominant_baseline="central",
            font_size=BASE_DAY_NUMBERS_TEXT_SIZE,
            font_family=TITLE_FONT_FAMILY
        ),
        svg.Text(
            text="Day 3",
            x=START_X+MAIN_OFFSET_X+(WIDTH*2.5), y=(START_Y+MAIN_OFFSET_Y)*0.85,
            text_anchor="middle",
            dominant_baseline="central",
            font_size=BASE_DAY_NUMBERS_TEXT_SIZE,
            font_family=TITLE_FONT_FAMILY
        ),
        svg.Text(
            text="Day 4",
            x=START_X+MAIN_OFFSET_X+(WIDTH*3.5), y=(START_Y+MAIN_OFFSET_Y)*0.85,
            text_anchor="middle",
            dominant_baseline="central",
            font_size=BASE_DAY_NUMBERS_TEXT_SIZE,
            font_family=TITLE_FONT_FAMILY
        ),
    ]

    return elements

def create_svg(classes):
    canvas = svg.SVG(
        width=CANVAS_WIDTH,
        height=CANVAS_HEIGHT,
        elements=draw_titles()+draw_day(START_X+MAIN_OFFSET_X, START_Y+MAIN_OFFSET_Y, [1,3,5,7], classes)+draw_day(START_X+MAIN_OFFSET_X+WIDTH, START_Y+MAIN_OFFSET_Y, [2,4,6,8], classes)+draw_day(START_X+MAIN_OFFSET_X+WIDTH+WIDTH, START_Y+MAIN_OFFSET_Y, [3,1,7,5], classes)+draw_day(START_X+MAIN_OFFSET_X+WIDTH+WIDTH+WIDTH, START_Y+MAIN_OFFSET_Y, [4,2,8,6], classes)+draw_sames(START_X+MAIN_OFFSET_X,START_Y+MAIN_OFFSET_Y)+draw_times(START_X+TIMES_OFFSET_X, START_Y+TIMES_OFFSET_Y)+draw_xday(START_X+XDAY_OFFSET_X, START_Y+XDAY_OFFSET_Y, classes)
    )
    return canvas

with open("file.svg", "w") as file:
    file.write(str(create_svg(CLASSES)))

# cairosvg.svg2png(url="file.svg", write_to="file.png")
