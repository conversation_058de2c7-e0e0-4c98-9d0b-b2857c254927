fetch('https://ljcds.myschoolapp.com/podium/default.aspx?t=52411&wapp=1&ch=1&svcid=edu', {
  method: 'GET',
  credentials: 'include'
})
.then(response => response.text())
.then(html => {
  const parser = new DOMParser();
  const doc = parser.parseFromString(html, 'text/html');

  const divs = doc.querySelectorAll('tr td div.dir2ItemUserInfoWrap, tr td div.dir2ItemUserInfoWrapAlt');
  const results = {};

  divs.forEach(div => {
    const nameElem = div.querySelector('div > span');
    const roomDiv = Array.from(div.querySelectorAll('div')).find(d => d.innerText.includes('Room:'));
    const teacherDiv = Array.from(div.querySelectorAll('div')).find(d => d.innerText.includes('Teacher:'));
    const blockDiv = Array.from(div.querySelectorAll('div')).find(d => d.innerText.includes('Block:'));

    let className = nameElem ? nameElem.textContent.trim() : '';

    // Remove unnecessary text from class name
    const dashNumIndex = className.search(/ - \d/);
    if (dashNumIndex !== -1) {
      className = className.slice(0, dashNumIndex).trim();
    }

    const roomText = roomDiv ? roomDiv.textContent.replace('Room:', '').trim() : '';
    const teacherText = teacherDiv ? teacherDiv.textContent.replace('Teacher:', '').trim() : '';
    const blockTextRaw = blockDiv ? blockDiv.textContent.replace('Block:', '').trim() : '';

    if (blockTextRaw && !isNaN(Number(blockTextRaw))) {
      const block = blockTextRaw;

      if (!results[block]) {
        results[block] = {
          name: [],
          room: [],
          teacher: []
        };
      }

      results[block].name.push(className);
      results[block].room.push(roomText);
      results[block].teacher.push(teacherText);
    }
  });

  const output = JSON.stringify(results, null, 2);

  navigator.clipboard.writeText(output)
    .then(() => alert('Class info copied to clipboard:\n\n' + output))
    .catch(() => alert('Failed to copy to clipboard. Please copy the data from the console:\n\n' + output));

  console.log('Extracted Class Info:', results);
})
.catch(err => {
  console.error('Failed to fetch or parse HTML:', err);
  alert('Failed to fetch or parse HTML. See console for details.');
});
