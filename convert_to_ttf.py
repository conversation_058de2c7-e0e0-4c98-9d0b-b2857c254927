from fontTools.ttLib import TTFont

# Input WOFF2 and output TTF
input_woff2 = "Jomhuria.woff2"
output_ttf = "Jomhuria-Regular.ttf"

# Load the WOFF2 font
font = TTFont(input_woff2)

# Save as TTF (TrueType-flavored OpenType)
# This preserves variable font axes, GSUB/GPOS tables, and metadata
font.flavor = None  # Remove WOFF2 flavoring
font.save(output_ttf)

print(f"Converted {input_woff2} → {output_ttf}")
